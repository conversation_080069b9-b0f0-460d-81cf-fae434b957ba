# My Projects - Monorepo

这是一个包含我所有小项目的单一仓库（Monorepo），便于统一管理和维护。

## 📁 项目结构

```
my-projects/
├── .git/                    # Git 版本控制
├── .gitignore              # Git 忽略文件配置
├── README.md               # 本文件 - 项目总览
├── mt4-rsi-strategy/       # MT4 RSI累积策略EA
│   ├── rsi.ex4            # 编译后的EA文件
│   └── README.md          # 项目详细说明
├── shared/                 # 共享资源
│   ├── utils/             # 通用工具函数
│   ├── configs/           # 共享配置文件
│   └── docs/              # 共享文档
└── [future-projects]/      # 未来的项目将在这里添加
```

## 🚀 当前项目

### 1. MT4 RSI累积策略EA (`mt4-rsi-strategy/`)
- **类型**: MetaTrader 4 专家顾问
- **功能**: 基于RSI累积值的自动交易策略
- **状态**: ✅ 完成
- **语言**: MQL4

## 📋 项目管理

### 添加新项目
1. 在根目录创建新的项目文件夹
2. 在项目文件夹中添加项目文件和README.md
3. 更新本README.md文件的项目列表
4. 提交更改到Git

### 命名规范
- 项目文件夹使用小写字母和连字符：`project-name`
- 每个项目都应包含自己的README.md文件
- 使用有意义的提交信息

### 版本管理
- 使用Git标签标记项目版本：`project-name/v1.0.0`
- 每个项目可以独立发布和版本控制
- 共享代码放在`shared/`目录中

## 🛠️ 开发环境

### 通用工具
- Git - 版本控制
- VS Code - 代码编辑器（推荐）

### 项目特定工具
- **MT4项目**: MetaTrader 4 平台
- **Web项目**: Node.js, npm/yarn
- **Python项目**: Python 3.x, pip

## 📝 贡献指南

1. 为每个新项目创建独立的文件夹
2. 保持项目间的独立性
3. 将可复用的代码放在`shared/`目录
4. 更新相关文档
5. 使用清晰的提交信息

## 📄 许可证

各项目可能有不同的许可证，请查看具体项目的README.md文件。

## 📞 联系方式

如有问题或建议，请通过Git提交issue或直接联系项目维护者。

---

*最后更新: 2025-08-01*
